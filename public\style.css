/* Base responsive setup */
* {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  color: #ffad33;
  font-family: 'Xanh Mono', monospace;
  background: url("Bull.jpg");
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center;
  background-size: cover;
  margin: 0;
  padding: 20px;
  min-height: 100vh;
  overflow-x: hidden;
}

div {
  
}

main {
  width: 95%;
  max-width: 1080px;
  padding: 20px 10px;
  margin: 0 auto;
}

h1 {
  color: #2080df;
  font-size: clamp(2em, 5vw, 3.75em);
  text-align: center;
  margin: 10px 0;
  word-wrap: break-word;
}

h2 {
  color: #2080df;
  font-size: clamp(1.5em, 4vw, 2.25em);
  text-align: center;
  margin: 10px 0;
  word-wrap: break-word;
}

p {
  font-size: clamp(0.9em, 2.5vw, 1.1em);
  line-height: 1.5;
  margin: 10px 0;
  word-wrap: break-word;
}

a {
  text-decoration: none;
  color: #f8d827;
}

/* unvisited link */
a:link {
  color: #f8d827;
}

/* visited link */
a:visited {
  color: #f8d827;
}

/* mouse over link */
a:hover {
  color: #f8d827;
  font-weight: 1000;
}

/* selected link */
a:active {
  color: #a6a6a6;
}

button {
  background-color: #ffad33;
  color: #2080df;
  border: 2px solid black;
  border-radius: 8px;
  min-width: 120px;
  max-width: 100%;
  padding: 12px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-family: 'Xanh Mono', monospace;
  font-size: clamp(14px, 3vw, 18px);
  font-weight: 900;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 5px;
  word-wrap: break-word;
  touch-action: manipulation;
}

button:hover, button:focus {
  background-color: #e0e0d1;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

button:active {
  transform: translateY(0);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.logo {
  color: #a6a6a6;
  font-size: 2.8em;
  margin-top: 1px;
  margin-bottom: -45px;
}

mark { 
  background-color: #ffad33;
}

.chum {
  color: #ffad33;
}

.cat {
  color: #ed4a1a;
  font-weight: bold;
}

/* Container and layout responsive styles */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  width: 100%;
}

/* Form styles */
form {
  max-width: 100%;
  margin: 0 auto;
}

input, select, textarea {
  width: 100%;
  max-width: 400px;
  padding: 12px;
  margin: 8px 0;
  border: 2px solid #ffad33;
  border-radius: 8px;
  font-size: 16px;
  font-family: 'Xanh Mono', monospace;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  box-sizing: border-box;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #2080df;
  box-shadow: 0 0 5px rgba(32, 128, 223, 0.5);
}

label {
  display: block;
  margin: 10px 0 5px 0;
  font-weight: bold;
  color: #ffad33;
}

/* Image responsive */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10px auto;
}

/* Table responsive */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

table, th, td {
  border: 1px solid #ffad33;
}

th, td {
  padding: 8px 12px;
  text-align: left;
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  body {
    background-attachment: scroll;
    padding: 15px;
    box-sizing: border-box;
    overflow-x: hidden;
  }

  main {
    padding: 15px 10px;
    width: 100%;
    box-sizing: border-box;
  }

  .container {
    padding: 15px;
    box-sizing: border-box;
    width: 100%;
    max-width: 100%;
  }

  button {
    width: 100%;
    max-width: 300px;
    margin: 8px auto;
    display: block;
    font-size: 16px;
    padding: 15px;
  }

  .logo {
    font-size: 2em;
    margin-bottom: -20px;
  }

  table {
    font-size: 14px;
  }

  th, td {
    padding: 6px 8px;
  }
}

/* Tablet styles */
@media (min-width: 769px) and (max-width: 1024px) {
  main {
    width: 90%;
    padding: 25px;
  }

  .container {
    padding: 25px;
  }

  button {
    width: auto;
    min-width: 200px;
  }
}

/* Large screen styles */
@media (min-width: 1025px) {
  main {
    width: 85%;
    max-width: 1200px;
  }

  .container {
    padding: 30px;
  }
}
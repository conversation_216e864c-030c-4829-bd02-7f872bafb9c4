<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>$tock Trade Generator</title>
  <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💵</text></svg>">
  <link href="/style.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link href="https://fonts.googleapis.com/css2?family=Xanh+Mono&display=swap" rel="stylesheet">
  <style>
    html { 
      background: url(images/bg.jpg) no-repeat center center fixed; 
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>💵 $tock Trade Generator</h1>
    <h2>For Top 5 Best Performing Stocks Right Now</h2>
    <p align='center' class='cat'>┍————- /ᐠ｡ꞈ｡ᐟ\ ————┑</p>
    <p align='center'; style="color:#ffad33; font-size:15px; font-style:italic; font-weight:bold">Created by Hector Garingalao Sevilla</p>
    <p align='center' class='cat'>┕————(..)(..) ∫∫————-┙</p>
    <!-- Show different buttons based on authentication status -->
    <div class="no-auth-required">
      <p align='center'><button onclick="document.location='/login'">Login</button></p>
      <p align='center'><button onclick="document.location='/signup'">Sign Up</button></p>
      <p align='center'><a href="/app" style="color: #ffad33; text-decoration: underline;">Continue as Guest</a></p>
    </div>

    <div class="auth-required" style="display: none;">
      <p align='center'><button onclick="document.location='/app'">Begin App</button></p>
      <p align='center'><button onclick="document.location='/saved-results'">View Saved Results</button></p>
      <p align='center'><button onclick="handleSignOut()" style="background-color: #f44336;">Logout</button></p>
    </div>

    <main></main>
    <p><a href="https://app.webull.com/trade" target="_blank">👋 Realtime Stock Charts Here!</a></p>
  </div>

  <script type="module" src="/firebase-config.js"></script>
  <script type="module">
    window.handleSignOut = async function() {
      try {
        // Check if Firebase is loaded and user is signed in
        if (typeof window.signOutUser === 'function') {
          const result = await window.signOutUser();
          if (result.success) {
            console.log('Sign out successful');
            window.location.reload();
          } else {
            console.error('Sign out failed:', result.error);
            alert('Error signing out: ' + result.error);
          }
        } else {
          console.log('Firebase not loaded, just reloading page');
          window.location.reload();
        }
      } catch (error) {
        console.error('Sign out error:', error);
        // Don't show alert for sign-out errors, just reload
        window.location.reload();
      }
    };
  </script>
</body>
</html>

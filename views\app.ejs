<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>$tock Trade Generator</title>
  <link href="/style2.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link href="https://fonts.googleapis.com/css2?family=Xanh+Mono&display=swap" rel="stylesheet">
  <style>
    html { 
      background: url(images/bg.jpg) no-repeat center center fixed; 
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
    }
    h1 a:hover {
      font-weight: 1000 !important;
    }
    .stock-entry {
      margin: 15px 0;
      padding: 15px;
      border: 2px solid #ffad33;
      border-radius: 10px;
      background-color: rgba(255, 255, 255, 0.1);
    }
    .button-group {
      text-align: center;
      margin: 20px 0;
    }
    .button-group button {
      margin: 5px;
    }
    @media (max-width: 768px) {
      body {
        padding: 15px !important;
        box-sizing: border-box;
      }

      .container {
        padding: 10px !important;
        box-sizing: border-box;
        width: 100%;
        max-width: 100%;
      }

      .button-group button {
        display: block;
        width: calc(100% - 20px);
        max-width: 280px;
        margin: 10px auto;
        box-sizing: border-box;
      }

      input[type="text"], input[type="number"] {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
      }

      .stock-entry {
        margin: 15px 0;
        padding: 10px;
        box-sizing: border-box;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1><a href="/" style="color: #2080df; text-decoration: none;">💵 $tock Trade Generator</a></h1>
    <h2>For Top 5 Best Performing Stocks Right Now</h2>
    <p class='cat' align='center'>┍————- /ᐠ｡ꞈ｡ᐟ\ ————┑</p>
    <p align='center'; style="color:#ed4a1a; font-size:15px; font-style:italic; font-weight:bold">Created by Hector Garingalao Sevilla</p>
    <p class='cat' align='center'>┕————(..)(..) ∫∫————-┙</p>

    <!-- Authentication status display -->
    <div class="auth-required" style="display: none; text-align: center; margin: 20px 0;">
      <p style="color: #2080df;">Welcome back! <span id="userEmail"></span></p>
      <p>
        <a href="/saved-results" style="margin: 0 15px;">📁 View Saved Results</a>
        <a href="#" onclick="handleSignOut()" style="margin: 0 15px;">🚪 Logout</a>
      </p>
    </div>

    <div class="no-auth-required" style="text-align: center; margin: 20px 0;">
      <p style="color: #666;">You're using guest mode. <a href="/login">Login</a> to save your results.</p>
    </div>
    <form action="/submit" method="post" id="stockForm">
      <p>
        <label for="name">What is your First Name?</label>
        <input type="text" id="name" name="name" required>
      </p>
      <p>
        <label for="capital">What is your total initial capital?</label>
        <input type="number" id="capital" name="capital" required>
      </p>

      <div id="stockEntries">
        <!-- Stock entries will be dynamically generated here -->
      </div>

      <div class="button-group">
        <button type="button" id="addStockBtn" class="add-btn">➕ Add Stock</button>
        <button type="button" id="removeStockBtn" class="remove-btn">➖ Remove Stock</button>
      </div>

      <div class="button-group">
        <button type="submit">🚀 Generate Analysis</button>
      </div>
    </form>

    <script>
      let stockCount = 0;

      function addStockEntry() {
        stockCount++;
        const stockEntries = document.getElementById('stockEntries');

        const stockDiv = document.createElement('div');
        stockDiv.className = 'stock-entry';
        stockDiv.id = `stock-${stockCount}`;

        stockDiv.innerHTML = `
          <p>
            <label for="stockCompany${stockCount}">What is the name or symbol of stock company ${stockCount}?</label>
            <input type="text" id="stockCompany${stockCount}" name="stockCompany${stockCount}" required><br>
            <label for="stock${stockCount}Price">What is the current price of stock company ${stockCount}?</label>
            <input type="number" id="stock${stockCount}Price" name="stock${stockCount}Price" step="0.01" min="0" required>
          </p>
        `;

        stockEntries.appendChild(stockDiv);
        updateRemoveButtonVisibility();
      }

      function removeStockEntry() {
        if (stockCount > 2) {
          const stockToRemove = document.getElementById(`stock-${stockCount}`);
          stockToRemove.remove();
          stockCount--;
          updateRemoveButtonVisibility();
        }
      }

      function updateRemoveButtonVisibility() {
        const removeBtn = document.getElementById('removeStockBtn');
        // Always show the button, but disable it when stockCount <= 2
        removeBtn.style.display = 'inline-block';
        if (stockCount <= 2) {
          removeBtn.disabled = true;
          removeBtn.style.opacity = '0.5';
          removeBtn.style.cursor = 'not-allowed';
        } else {
          removeBtn.disabled = false;
          removeBtn.style.opacity = '1';
          removeBtn.style.cursor = 'pointer';
        }
      }

      // Initialize with 5 stock entries
      addStockEntry();
      addStockEntry();
      addStockEntry();
      addStockEntry();
      addStockEntry();

      // Event listeners
      document.getElementById('addStockBtn').addEventListener('click', addStockEntry);
      document.getElementById('removeStockBtn').addEventListener('click', removeStockEntry);

      // Add hidden input to track stock count on form submission
      document.getElementById('stockForm').addEventListener('submit', function() {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'stockCount';
        hiddenInput.value = stockCount;
        this.appendChild(hiddenInput);
      });
    </script>

    <script type="module" src="/firebase-config.js"></script>
    <script type="module">
      let currentUser = null;

      // Check authentication state
      window.firebaseAuth.onAuthStateChanged((user) => {
        currentUser = user;
        if (user) {
          document.getElementById('userEmail').textContent = user.email;
        }
      });

      window.handleSignOut = async function() {
        try {
          if (typeof window.signOutUser === 'function') {
            const result = await window.signOutUser();
            if (result.success) {
              window.location.href = '/';
            } else {
              console.error('Sign out failed:', result.error);
              // Still redirect even if sign out fails
              window.location.href = '/';
            }
          } else {
            console.log('Firebase not loaded, redirecting to home');
            window.location.href = '/';
          }
        } catch (error) {
          console.error('Sign out error:', error);
          // Don't show alert, just redirect
          window.location.href = '/';
        }
      };
    </script>

    <main></main>
    <div style="margin-top: 30px;">
      <p align='center'><a href="https://www.barchart.com/stocks/highs-lows/highs?timeFrame=1m&orderBy=percentChange&orderDir=desc" target="_blank">👋 Top Market Leaders [US Exchanges 1-Month New Highs] Are Here!</a></p>
      <p align='center'><a href="https://www.barchart.com/stocks/most-active/price-volume-leaders?orderBy=percentChange&orderDir=desc&viewName=main" target="_blank">👋 Top Market Leaders [US Exchanges Price Volume] Are Here!</a></p>
      <p class="bottom" align='center' style="margin-bottom: 30px;"><a href="https://www.barchart.com/stocks/performance/percent-change/advances?viewName=technical&orderBy=opinion&orderDir=desc" target="_blank">👋 Top Market Leaders [US Exchanges Percent Change Advances] Are Here!</a></p>
    </div>
  </div>
</body>
</html>

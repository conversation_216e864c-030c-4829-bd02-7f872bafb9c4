html {
  height: 100%;
}

body {
  color: #000000;
  font-family: 'Xanh Mono', monospace;
  box-sizing: border-box;
  background-color: ;
  background: url("Money.jpg");
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center;
  background-size: cover;
  margin: 0;
  padding: 20px;
  min-height: 100vh;
}

div {
  
}

main {
  width: 90%;
  max-width: 1080px;
  padding: 40px 20px 20px;
  margin: 0 auto;
}

/* Container styles for app page */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  width: 100%;
}

h1 {
  color: #2080df;
  font-size: 3.75em;
}

h2 {
  color: #2080df;
  font-size: 2.25em;
}

p {
  font-size: 1.1em;
  line-height: 1.5;
}

a {
  text-decoration: none;
  color: #1d1202;
}

/* unvisited link */
a:link {
  color: #1d1202;
}

/* visited link */
a:visited {
  color: #1d1202;
}

/* mouse over link */
a:hover {
  color: #1d1202;
  font-weight: 1000;
}

/* selected link */
a:active {
  color: #a6a6a6;
}

button {
  background-color: #ffad33;
  color: #2080df;
  border: 2px solid black;
  border-radius: ;
  width: 265px;
  padding: 15px 32px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-family: 'Xanh Mono', monospace;
  font-size: 20px;
  font-weight: 900;
}

button:hover {
  background-color: #e0e0d1;
}

.add-btn, .remove-btn {
  width: 150px;
  padding: 10px 20px;
  margin: 5px;
  font-size: 16px;
}

.add-btn {
  background-color: #4CAF50;
  color: white;
}

.add-btn:hover {
  background-color: #45a049;
}

.remove-btn {
  background-color: #f44336;
  color: white;
  display: none;
}

.remove-btn:hover {
  background-color: #da190b;
}

.stock-entry {
  margin: 10px 0;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.1);
}

.logo {
  color: #a6a6a6;
  font-size: 2.8em;
  margin-top: 1px;
  margin-bottom: -45px;
}

mark { 
  background-color: #ffad33;
}

.chum {
  color: #ffad33;
}

.cat {
  font-weight: bold;
}

@media (max-width: 768px) {
  body {
    padding: 15px;
    background-attachment: scroll;
  }

  main {
    padding: 20px 10px;
    width: 100%;
  }

  .container {
    padding: 15px;
  }

  h1 {
    font-size: 2.5em;
  }

  h2 {
    font-size: 1.8em;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    padding: 25px;
  }

  main {
    padding: 40px 25px 25px;
  }
}

@media (min-width: 1025px) {
  .container {
    padding: 30px;
  }

  main {
    padding: 40px 30px 30px;
  }
}
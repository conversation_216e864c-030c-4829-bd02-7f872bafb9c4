<!-- result.ejs -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Stock Trade Results</title>
  <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💵</text></svg>">
  <link href="/style3.css" rel="stylesheet">
  <style>
    html { 
      background: url(images/bg.jpg) no-repeat center center fixed; 
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
    }
    img {
      width: 100%;
      height: auto;
    }
    h1 a:hover {
      font-weight: 1000 !important;
    }
    .button-group {
      text-align: center;
      margin: 20px 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 10px;
    }
    .button-group button {
      flex: 0 1 auto;
      min-width: 120px;
    }
    @media (max-width: 768px) {
      .button-group {
        flex-direction: column;
        align-items: center;
      }
      .button-group button {
        width: 100%;
        max-width: 300px;
        margin: 5px 0;
      }
      h1, h2, h3 {
        font-size: clamp(16px, 4vw, 28px) !important;
      }
      p {
        font-size: clamp(14px, 3vw, 16px);
        padding: 0 10px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 style="font-size:38px; font-family:'Courier New'" align='center'><a href="/" style="color: #2080df; text-decoration: none;">💵 $tock Trade Generator</a></h1>
    <h2 style="font-size:28px; font-family:'Courier New'" align='center'>For Top Best Performing Stocks Right Now</h2>

    <p align='center'>░▒▓█▓░▒▓█▓▒░▒▓█▓▒░▒▓</p>
    <p align="center"><img src="/Stock.jpg" width="605" height="454" alt="Stock Image"></p>
    <p align='center'>░▒▓█▓░▒▓█▓▒░▒▓█▓▒░▒▓</p>

    <h3 style="font-size:20px; font-family:'Courier New'"><center>You're total initial capital is <%= capital %> USD.</center></h3>
    <% stockPrices.forEach(function(stock) { %>
      <p><center>Current price for '<i><%= stock.name %></i>' is <b><%= stock.price %></b>.</center></p>
    <% }); %>

    <center>💻</center>

    <h3 style="font-size:20px; font-family:'Courier New'"><center>Amounts for Robinhood</center></h3>
    <% investments.forEach(function(investment) { %>
      <p><i><center>Amount to 'BUY' for <b><%= investment.name %></b> is <mark><b><u><%= investment.amount.toFixed(2) %></u></b></mark>.</center></i></p>
    <% }); %>

    <center>📲</center>

    <h3 style="font-size:20px; font-family:'Courier New'"><center>Shares for E*TRADE</center></h3>
    <p><i><center>Number of equal shares to 'BUY' for
      <% stockPrices.forEach(function(stock, index) { %>
        <b><%= stock.name %></b><% if (index < stockPrices.length - 1) { %>, <% } %>
      <% }); %>
      is <mark><b><u><%= shareBuyAverage %></u></b></mark> 'each'.</center></i></p>

    <h4 style="font-size:16px; font-family:'Courier New'"><b><center>☞ Note that if you get a <i>0</i>, that means <i><u>INCALCULABLE</u></i>. ☜</center></b></h4>

    <h3><center>Ⰶ║ ⵈ ⵈ ⵈ ⵈ ⵈ ⵈ║Ⰶ</center></h3>
    <p align="center"><img src="/Horn.jpg" width="648" height="432" alt="Horn Image"></p>

    <h3 style="font-size:18px; font-family:'Courier New'" align="center">☝ Just for checking purposes, you're total invested amount for all <%= stockPrices.length %> stocks in 'Robinhood' is <%= totalInvestmentAmount.toFixed(2) %> USD. Or, if using 'E*TRADE', then your total invested amount for all <%= stockPrices.length %> stocks is <%= totalAmountShares.toFixed(2) %> USD.</h3>

    <h3 style="font-size:18px; font-family:'Courier New'"><center>🥧 You're slice of the pie from your 'Total Investment Capital' down below:</center></h3>
    <% percentages.forEach(function(percentage) { %>
      <h4 style="font-size:18px; font-family:'Courier New'" align="center">✱ <%= percentage.name %> comprises <%= percentage.percent %> %</h4>
    <% }); %>

    <h2 style="font-size:22px; font-family:'Courier New'"><center>Good Luck! <%= name %> <div class="chum">(▰˘◡˘▰)</div></center></h2>

    <div class="button-group">
      <button onclick="printPage()">🖨️ Print Page</button>
      <button onclick="saveAsImage()">📷 Save as Image</button>
      <button onclick="saveResults()" class="auth-required" id="saveBtn">💾 Save Results</button>
    </div>

    <div class="button-group">
      <button onclick="document.location='/saved-results'" class="auth-required">📁 View Saved Results</button>
      <button onclick="document.location='/app'">👋 Start Over</button>
    </div>

    <div class="no-auth-required" style="text-align: center; margin: 20px 0; color: #666;">
      <p><a href="/login">Login</a> to save your results for future reference.</p>
    </div>
  </div>

  <!-- Notification area for messages -->
  <div id="notificationArea" style="position: fixed; top: 20px; left: 50%; transform: translateX(-50%); z-index: 10000; width: 90%; max-width: 500px; display: none;">
    <div id="notificationMessage" style="padding: 15px 20px; border-radius: 8px; color: white; font-weight: bold; text-align: center; box-shadow: 0 4px 12px rgba(0,0,0,0.3); font-family: 'Xanh Mono', monospace;"></div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script>
    // Notification system
    function showNotification(message, type = 'info') {
      const notificationArea = document.getElementById('notificationArea');
      const notificationMessage = document.getElementById('notificationMessage');

      // Set message and styling based on type
      notificationMessage.textContent = message;

      switch(type) {
        case 'success':
          notificationMessage.style.backgroundColor = '#4CAF50';
          break;
        case 'error':
          notificationMessage.style.backgroundColor = '#f44336';
          break;
        case 'warning':
          notificationMessage.style.backgroundColor = '#ff9800';
          break;
        default:
          notificationMessage.style.backgroundColor = '#2080df';
      }

      // Show notification
      notificationArea.style.display = 'block';

      // Auto-hide after 5 seconds
      setTimeout(() => {
        notificationArea.style.display = 'none';
      }, 5000);
    }

    function printPage() {
      window.print();
    }

    function saveAsImage() {
      const element = document.querySelector('.container');
      const options = {
        backgroundColor: '#ffffff',
        scale: 2,
        useCORS: true,
        allowTaint: true,
        scrollX: 0,
        scrollY: 0,
        width: element.scrollWidth,
        height: element.scrollHeight
      };

      html2canvas(element, options).then(function(canvas) {
        // Create download link
        const link = document.createElement('a');
        link.download = 'stock-trade-results-<%= name %>.png';
        link.href = canvas.toDataURL('image/png');

        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }).catch(function(error) {
        console.error('Error generating image:', error);
        showNotification('Error saving image. Please try again or use the print function.', 'error');
      });
    }

    // Firebase functionality
    let currentUser = null;
    let resultsData = {
      name: '<%= name %>',
      capital: <%= capital %>,
      stockPrices: <%- JSON.stringify(stockPrices) %>,
      investments: <%- JSON.stringify(investments) %>,
      shares: <%- JSON.stringify(shares) %>,
      percentages: <%- JSON.stringify(percentages) %>,
      shareBuyAverage: <%= shareBuyAverage %>,
      totalInvestmentAmount: <%= totalInvestmentAmount %>,
      totalAmountShares: <%= totalAmountShares %>
    };

    async function saveResults() {
      console.log('Save results called');
      console.log('Current user:', currentUser);

      // Check if Firebase is loaded
      if (typeof window.firebaseAuth === 'undefined') {
        showNotification('Firebase is not loaded. Please refresh the page and try again.', 'error');
        return;
      }

      // Check if user is authenticated
      if (!currentUser) {
        showNotification('Please login to save your results.', 'warning');
        setTimeout(() => {
          window.location.href = '/login';
        }, 2000);
        return;
      }

      const saveBtn = document.getElementById('saveBtn');
      if (!saveBtn) {
        console.error('Save button not found');
        return;
      }

      const originalText = saveBtn.innerHTML;
      saveBtn.innerHTML = '💾 Saving...';
      saveBtn.disabled = true;

      try {
        console.log('Attempting client-side save to Firestore...');

        // Try client-side saving first (works with current Firebase setup)
        if (typeof window.saveStockResults === 'function') {
          console.log('Using client-side Firebase save');
          const result = await window.saveStockResults(currentUser.uid, resultsData);

          if (result.success) {
            showNotification('Results saved successfully!', 'success');
            saveBtn.innerHTML = '✅ Saved';
            setTimeout(() => {
              saveBtn.innerHTML = originalText;
              saveBtn.disabled = false;
            }, 2000);
            return;
          } else {
            console.log('Client-side save failed, trying server-side...');
          }
        }

        // Fallback to server-side saving
        console.log('Getting ID token for server-side save...');
        const idToken = await currentUser.getIdToken();
        console.log('ID token obtained');

        console.log('Sending data to server:', resultsData);

        const response = await fetch('/api/save-results', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${idToken}`
          },
          body: JSON.stringify(resultsData)
        });

        console.log('Response status:', response.status);

        const result = await response.json();
        console.log('Server response:', result);

        if (result.success) {
          showNotification('Results saved successfully!', 'success');
          saveBtn.innerHTML = '✅ Saved';
          setTimeout(() => {
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
          }, 2000);
        } else {
          // Handle server-side Firebase not configured
          if (result.instructions && Array.isArray(result.instructions)) {
            const instructionsText = result.instructions.join('\n');
            showNotification(`Server-side saving not available. ${result.error}. Note: Client-side saving should work without additional setup.`, 'warning');
          } else {
            showNotification(`Error: ${result.error || 'Failed to save results'}`, 'error');
          }
          saveBtn.innerHTML = originalText;
          saveBtn.disabled = false;
        }
      } catch (error) {
        console.error('Error saving results:', error);

        // Try to get more detailed error info
        let errorMessage = error.message;
        if (error.message.includes('HTTP error! status: 500')) {
          errorMessage = 'Server configuration issue. Client-side Firebase should still work.';
        }

        showNotification(`Error saving results: ${errorMessage}`, 'error');
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
      }
    }
  </script>

  <script type="module" src="/firebase-config.js"></script>
  <script type="module">
    // Wait for Firebase to load, then check authentication state
    let firebaseLoadAttempts = 0;
    const maxAttempts = 50; // 5 seconds max wait

    function waitForFirebase() {
      if (typeof window.firebaseAuth !== 'undefined' && window.firebaseAuth) {
        console.log('Firebase loaded, setting up auth listener');
        window.firebaseAuth.onAuthStateChanged((user) => {
          console.log('Auth state changed:', user ? user.email : 'No user');
          currentUser = user;

          // Update UI based on auth state
          const authElements = document.querySelectorAll('.auth-required');
          const noAuthElements = document.querySelectorAll('.no-auth-required');

          if (user) {
            authElements.forEach(el => el.style.display = 'inline-block');
            noAuthElements.forEach(el => el.style.display = 'none');
          } else {
            authElements.forEach(el => el.style.display = 'none');
            noAuthElements.forEach(el => el.style.display = 'block');
          }
        });
      } else if (firebaseLoadAttempts < maxAttempts) {
        firebaseLoadAttempts++;
        setTimeout(waitForFirebase, 100);
      } else {
        console.error('Firebase failed to load after 5 seconds');
        // Hide auth-required elements if Firebase fails to load
        const authElements = document.querySelectorAll('.auth-required');
        authElements.forEach(el => el.style.display = 'none');
      }
    }

    waitForFirebase();
  </script>
</body>
</html>

html {
  height: 100%;
}

body {
  color: #000000;
  font-family: 'Courier New', monospace;
  box-sizing: border-box;
  background-color: ;
  background: url("White.jpg");
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center;
  background-size: cover;
  margin: 0;
  padding: 20px;
  min-height: 100vh;
}

div {
  
}

main {
  width: 90%;
  max-width: 1080px;
  padding: 40px 20px 20px;
  margin: 0 auto;
}

/* Container styles for results pages */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  width: 100%;
}

h1 {
  color: #2080df;
  font-size: 3.75em;
}

h2 {
  color: #2080df;
  font-size: 2.25em;
}

p {
  font-size: 1.1em;
  line-height: 1.5;
}

a {
  text-decoration: none;
  color: #ffad33;
}

/* unvisited link */
a:link {
  color: #ffad33;
}

/* visited link */
a:visited {
  color: #ffad33;
}

/* mouse over link */
a:hover {
  color: #ffad33;
  font-weight: 1000;
}

/* selected link */
a:active {
  color: #a6a6a6;
}

button {
  background-color: #ffad33;
  color: #2080df;
  border: 2px solid black;
  border-radius: ;
  width: 265px;
  padding: 15px 32px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-family: 'Xanh Mono', monospace;
  font-size: 20px;
  font-weight: 900;
}

button:hover {
  background-color: #e0e0d1;
}

/* Action buttons for print and save */
button[onclick*="printPage"], button[onclick*="saveAsImage"] {
  width: 180px;
  padding: 12px 24px;
  font-size: 16px;
  margin: 5px;
  font-family: 'Xanh Mono', monospace;
  font-weight: 900;
}

button[onclick*="printPage"] {
  background-color: #4CAF50;
  color: white;
}

button[onclick*="printPage"]:hover {
  background-color: #45a049;
}

button[onclick*="saveAsImage"] {
  background-color: #2196F3;
  color: white;
}

button[onclick*="saveAsImage"]:hover {
  background-color: #1976D2;
}

/* Print-specific styles */
@media print {
  button {
    display: none !important;
  }

  .container {
    margin: 0;
    padding: 30px;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

.logo {
  color: #a6a6a6;
  font-size: 2.8em;
  margin-top: 1px;
  margin-bottom: -45px;
}

mark { 
  background-color: #ffad33;
}

.chum {
  color: #ffad33;
}

.cat {
  font-weight: bold;
}

@media (max-width: 768px) {
  body {
    padding: 15px;
    background-attachment: scroll;
    box-sizing: border-box;
    overflow-x: hidden;
  }

  main {
    padding: 20px 10px;
    width: 100%;
    box-sizing: border-box;
  }

  .container {
    padding: 15px;
    box-sizing: border-box;
    width: 100%;
    max-width: 100%;
  }

  h1 {
    font-size: 2.5em;
  }

  h2 {
    font-size: 1.8em;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    padding: 25px;
  }

  main {
    padding: 40px 25px 25px;
  }
}

@media (min-width: 1025px) {
  .container {
    padding: 30px;
  }

  main {
    padding: 40px 30px 30px;
  }
}
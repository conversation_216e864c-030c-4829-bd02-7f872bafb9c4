<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sign Up - $tock Trade Generator</title>
  <link href="/style.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link href="https://fonts.googleapis.com/css2?family=Xanh+Mono&display=swap" rel="stylesheet">
  <style>
    html { 
      background: url(images/bg.jpg) no-repeat center center fixed; 
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
    }
    h1 a:hover {
      font-weight: 1000 !important;
    }
    .form-container {
      max-width: 400px;
      margin: 0 auto;
      padding: 20px;
    }
    @media (max-width: 768px) {
      .form-container {
        padding: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1><a href="/" style="color: #2080df; text-decoration: none;">💵 $tock Trade Generator</a></h1>
    <h2>Create Your Account</h2>
    <p class='cat' align='center'>┍————- /ᐠ｡ꞈ｡ᐟ\ ————┑</p>
    <p align='center'; style="color:#ffad33; font-size:15px; font-style:italic; font-weight:bold">Created by Hector Garingalao Sevilla</p>
    <p class='cat' align='center'>┕————(..)(..) ∫∫————-┙</p>
    
    <div class="form-container">
      <form id="signupFormElement">
        <p>
          <label for="email">Email:</label>
          <input type="email" id="email" name="email" required>
        </p>
        <p>
          <label for="password">Password (min 6 characters):</label>
          <input type="password" id="password" name="password" required minlength="6">
        </p>
        <p>
          <label for="confirmPassword">Confirm Password:</label>
          <input type="password" id="confirmPassword" name="confirmPassword" required minlength="6">
        </p>
        <p style="text-align: center;">
          <button type="submit">🚀 Sign Up</button>
        </p>
      </form>

      <p align="center">
        Already have an account? <a href="/login">Login here</a>
      </p>

      <p align="center">
        <a href="/app" style="color: #ffad33; text-decoration: underline;">Continue as Guest</a>
      </p>
    </div>
    
    <div id="errorMessage" style="color: red; text-align: center; display: none;"></div>
    <div id="successMessage" style="color: green; text-align: center; display: none;"></div>
    <div id="loadingMessage" style="text-align: center; display: none;">Creating account...</div>
    
    <main></main>
  </div>

  <script type="module" src="/firebase-config.js"></script>
  <script type="module">
    document.getElementById('signupFormElement').addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirmPassword').value;
      const errorDiv = document.getElementById('errorMessage');
      const successDiv = document.getElementById('successMessage');
      const loadingDiv = document.getElementById('loadingMessage');
      
      // Reset messages
      errorDiv.style.display = 'none';
      successDiv.style.display = 'none';
      
      // Validate passwords match
      if (password !== confirmPassword) {
        errorDiv.textContent = 'Passwords do not match.';
        errorDiv.style.display = 'block';
        return;
      }
      
      // Show loading
      loadingDiv.style.display = 'block';
      
      try {
        const result = await window.signUp(email, password);
        
        if (result.success) {
          successDiv.textContent = 'Account created successfully! Redirecting...';
          successDiv.style.display = 'block';
          
          // Redirect to main app after a short delay
          setTimeout(() => {
            window.location.href = '/app';
          }, 2000);
        } else {
          errorDiv.textContent = result.error;
          errorDiv.style.display = 'block';
        }
      } catch (error) {
        errorDiv.textContent = 'Sign up failed. Please try again.';
        errorDiv.style.display = 'block';
      }
      
      loadingDiv.style.display = 'none';
    });
  </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login - $tock Trade Generator</title>
  <link href="/style.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link href="https://fonts.googleapis.com/css2?family=Xanh+Mono&display=swap" rel="stylesheet">
  <style>
    html { 
      background: url(images/bg.jpg) no-repeat center center fixed; 
      -webkit-background-size: cover;
      -moz-background-size: cover;
      -o-background-size: cover;
      background-size: cover;
    }
    h1 a:hover {
      font-weight: 1000 !important;
    }
    .form-container {
      max-width: 400px;
      margin: 30px auto;
      padding: 30px;
    }
    @media (max-width: 768px) {
      .form-container {
        margin: 20px auto;
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1><a href="/" style="color: #2080df; text-decoration: none;">💵 $tock Trade Generator</a></h1>
    <h2>Login to Your Account</h2>
    <p class='cat' align='center'>┍————- /ᐠ｡ꞈ｡ᐟ\ ————┑</p>
    <p align='center'; style="color:#ffad33; font-size:15px; font-style:italic; font-weight:bold">Created by Hector Garingalao Sevilla</p>
    <p class='cat' align='center'>┕————(..)(..) ∫∫————-┙</p>
    
    <div class="form-container">
      <form id="loginFormElement">
        <p>
          <label for="email">Email:</label>
          <input type="email" id="email" name="email" required>
        </p>
        <p>
          <label for="password">Password:</label>
          <input type="password" id="password" name="password" required>
        </p>
        <p style="text-align: center;">
          <button type="submit">🔐 Login</button>
        </p>
      </form>

      <p align="center">
        Don't have an account? <a href="/signup">Sign up here</a>
      </p>

      <p align="center">
        <a href="/app" style="color: #ffad33; text-decoration: underline;">Continue as Guest</a>
      </p>
    </div>
    
    <div id="errorMessage" style="color: red; text-align: center; display: none;"></div>
    <div id="loadingMessage" style="text-align: center; display: none;">Logging in...</div>
    
    <main></main>
  </div>

  <script type="module" src="/firebase-config.js"></script>
  <script type="module">
    document.getElementById('loginFormElement').addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const email = document.getElementById('email').value;
      const password = document.getElementById('password').value;
      const errorDiv = document.getElementById('errorMessage');
      const loadingDiv = document.getElementById('loadingMessage');
      
      // Show loading
      loadingDiv.style.display = 'block';
      errorDiv.style.display = 'none';
      
      try {
        const result = await window.signIn(email, password);
        
        if (result.success) {
          // Redirect to main app
          window.location.href = '/app';
        } else {
          errorDiv.textContent = result.error;
          errorDiv.style.display = 'block';
        }
      } catch (error) {
        errorDiv.textContent = 'Login failed. Please try again.';
        errorDiv.style.display = 'block';
      }
      
      loadingDiv.style.display = 'none';
    });
  </script>
</body>
</html>
